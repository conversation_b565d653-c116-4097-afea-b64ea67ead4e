import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { TagModule } from 'primeng/tag';
import { DividerModule } from 'primeng/divider';
import { CardModule } from 'primeng/card';
import {
  AdWinnersConfigDialogData,
  AdWinnersConfigResult,
  AdWinnersExternalConfig,
} from '../models';

@Component({
  selector: 'app-ad-winners-config-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    DialogModule,
    CheckboxModule,
    TagModule,
    DividerModule,
    CardModule,
  ],
  template: `
    <p-dialog
      [visible]="visible"
      [modal]="true"
      [closable]="false"
      [draggable]="false"
      [resizable]="false"
      [style]="{ width: '800px', maxHeight: '80vh' }"
      styleClass="ad-winners-config-dialog"
      header="⚙️ Configure Facebook Accounts"
    >
      <div class="dialog-content" *ngIf="dialogData">
        <!-- Account Selection -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">
              <i class="pi pi-list"></i>
              Select Facebook Accounts
            </h3>
            <p-button
              label="Select All"
              icon="pi pi-check-square"
              [text]="true"
              size="small"
              (onClick)="selectAllAccounts()"
            >
            </p-button>
          </div>

          <div
            class="accounts-grid"
            *ngIf="dialogData.availableAccounts.length > 0"
          >
            <div
              *ngFor="let account of dialogData.availableAccounts"
              class="account-card"
            >
              <p-card>
                <div class="account-content">
                  <div class="account-header">
                    <p-checkbox
                      [binary]="true"
                      [(ngModel)]="selectedAccounts[account.ad_account_id]"
                      [inputId]="'account-' + account.ad_account_id"
                    >
                    </p-checkbox>
                    <div class="account-info">
                      <div class="account-name">
                        {{ account.ad_account_name }}
                      </div>
                      <div class="account-id">{{ account.ad_account_id }}</div>
                    </div>
                  </div>
                  <div class="account-meta">
                    <p-tag
                      [value]="getAccountStatusLabel(account.account_status)"
                      [severity]="
                        getAccountStatusSeverity(account.account_status)
                      "
                    >
                    </p-tag>
                    <span class="account-currency">{{ account.currency }}</span>
                  </div>
                  <div class="account-details">
                    <div class="detail-item">
                      <i class="fab fa-facebook-f"></i>
                      <span>{{ account.app_name }}</span>
                    </div>
                    <div class="detail-item">
                      <i class="pi pi-user"></i>
                      <span>{{ account.user_name }}</span>
                    </div>
                  </div>
                </div>
              </p-card>
            </div>
          </div>

          <div
            class="no-accounts"
            *ngIf="dialogData.availableAccounts.length === 0"
          >
            <i class="pi pi-info-circle"></i>
            <p>
              No Facebook accounts available. Please configure accounts in
              Facebook Accounts Manager first.
            </p>
          </div>
        </div>
      </div>

      <ng-template pTemplate="footer">
        <div class="dialog-footer">
          <p-button
            label="Cancel"
            icon="pi pi-times"
            [text]="true"
            (onClick)="onCancel()"
          >
          </p-button>
          <p-button
            label="Save Configuration"
            icon="pi pi-check"
            (onClick)="onSave()"
            [disabled]="!isValidConfiguration()"
          >
          </p-button>
        </div>
      </ng-template>
    </p-dialog>
  `,
  styles: [
    `
      .dialog-content {
        padding: 1rem 0;
      }

      .config-section {
        margin-bottom: 2rem;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: #5521be;
        margin: 0;
      }

      .accounts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1rem;
        max-height: 400px;
        overflow-y: auto;
        padding: 0.5rem;
      }

      .account-card {
        transition: transform 0.2s ease;
      }

      .account-card:hover {
        transform: translateY(-2px);
      }

      .account-content {
        padding: 0.5rem;
      }

      .account-header {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .account-info {
        flex: 1;
      }

      .account-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: #1f2937;
      }

      .account-id {
        font-size: 0.875rem;
        color: #6b7280;
        font-family: monospace;
      }

      .account-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
      }

      .account-currency {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
      }

      .account-details {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .detail-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
      }

      .detail-item i {
        width: 16px;
        text-align: center;
      }

      .no-accounts {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
      }

      .no-accounts i {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #9ca3af;
      }

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        padding-top: 1rem;
      }

      :host ::ng-deep .p-dialog .p-dialog-header {
        background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
        color: white;
      }

      :host ::ng-deep .p-dialog .p-dialog-header .p-dialog-title {
        font-weight: 600;
      }

      :host ::ng-deep .p-card {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
      }

      :host ::ng-deep .p-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      :host ::ng-deep .p-tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }

      :host ::ng-deep .p-divider {
        margin: 1.5rem 0;
      }
    `,
  ],
})
export class AdWinnersConfigDialogComponent implements OnInit {
  @Input() visible = false;
  @Output() result = new EventEmitter<AdWinnersConfigResult>();
  selectedAccounts: { [accountId: string]: boolean } = {};
  removedAccountIds: string[] = [];

  private _dialogData: AdWinnersConfigDialogData | null = null;

  get dialogData(): AdWinnersConfigDialogData | null {
    return this._dialogData;
  }

  @Input() set dialogData(data: AdWinnersConfigDialogData | null) {
    this._dialogData = data;
    this.initializeFromConfig();
  }

  ngOnInit(): void {
    // Initialization is handled in the dialogData setter
  }

  getAccountStatusLabel(status: number): string {
    const statusMap: { [key: number]: string } = {
      1: 'Active',
      2: 'Disabled',
      3: 'Unsettled',
      7: 'Pending Review',
      8: 'Pending Settlement',
      9: 'In Grace Period',
      101: 'Temporarily Unavailable',
      102: 'Pending Closure',
    };
    return statusMap[status] || 'Unknown';
  }

  getAccountStatusSeverity(
    status: number,
  ): 'success' | 'info' | 'warning' | 'danger' {
    if (status === 1) return 'success';
    if ([7, 8, 9].includes(status)) return 'warning';
    if ([2, 101, 102].includes(status)) return 'danger';
    return 'info';
  }

  selectAllAccounts(): void {
    if (this._dialogData?.availableAccounts) {
      this._dialogData.availableAccounts.forEach((account) => {
        this.selectedAccounts[account.ad_account_id] = true;
      });
    }
  }

  isValidConfiguration(): boolean {
    // Configuration is always valid - can save with no accounts selected
    return true;
  }

  onSave(): void {
    const selectedAccountIds = Object.keys(this.selectedAccounts).filter(
      (id) => this.selectedAccounts[id],
    );

    const config: AdWinnersExternalConfig = {
      selectedAccounts: selectedAccountIds,
    };

    this.result.emit({
      config,
      confirmed: true,
    });
  }

  onCancel(): void {
    this.result.emit({
      config: {} as AdWinnersExternalConfig,
      confirmed: false,
    });
  }

  private initializeFromConfig(): void {
    // Reset selected accounts
    this.selectedAccounts = {};

    if (this._dialogData?.currentConfig && this._dialogData?.availableAccounts) {
      const config = this._dialogData.currentConfig;
      const availableAccountIds = new Set(
        this._dialogData.availableAccounts.map(acc => acc.ad_account_id)
      );

      // Initialize selected accounts from saved config, but only for accounts that still exist
      if (config.selectedAccounts) {
        const validAccountIds: string[] = [];
        const invalidAccountIds: string[] = [];

        config.selectedAccounts.forEach((accountId) => {
          if (availableAccountIds.has(accountId)) {
            this.selectedAccounts[accountId] = true;
            validAccountIds.push(accountId);
          } else {
            invalidAccountIds.push(accountId);
          }
        });

        // Log information about invalid accounts for debugging
        if (invalidAccountIds.length > 0) {
          console.warn(
            `Ad Winners configuration contains ${invalidAccountIds.length} account(s) that no longer exist:`,
            invalidAccountIds
          );
        }
      }
    }
  }
}
